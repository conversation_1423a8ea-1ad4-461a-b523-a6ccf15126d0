'use client';

import React from 'react';
import LocationCard from './LocationCard';
import FlightCard from './FlightCard';

const CardsDemo = () => {
  // Sample location data
  const sampleLocation = {
    id: 'berlin-001',
    name: 'Berlin',
    country: 'Germany',
    description: 'Experience the vibrant capital of Germany with its rich history, world-class museums, and dynamic nightlife. From the Brandenburg Gate to the Berlin Wall Memorial, discover a city where past and present seamlessly blend.',
    image: 'https://images.unsplash.com/photo-1560969184-10fe8719e047?w=500',
    duration: '3-5 days',
    bestTime: 'May - Sep',
    activities: ['Museums', 'Historical Tours', 'Nightlife', 'Food Tours'],
    highlights: [
      'Brandenburg Gate and Pariser Platz',
      'Museum Island with world-renowned collections',
      'East Side Gallery - longest remaining Berlin Wall section'
    ],
    estimatedCost: {
      currency: '€',
      amount: '80-120',
      period: 'per day'
    },
    rating: 4.6,
    tags: ['History', 'Culture', 'Urban', 'Museums']
  };

  // Sample flight data
  const sampleFlight = {
    id: 'LH123',
    airline: {
      name: '<PERSON><PERSON><PERSON><PERSON>',
      logo: 'https://logos-world.net/wp-content/uploads/2020/03/Lufthansa-Logo.png',
      code: 'LH 123'
    },
    departure: {
      airport: 'LHR',
      city: 'London',
      time: '08:30',
      date: '28 Aug 2025',
      terminal: 'T2'
    },
    arrival: {
      airport: 'FRA',
      city: 'Frankfurt',
      time: '11:45',
      date: '28 Aug 2025',
      terminal: 'T1'
    },
    duration: '2h 15m',
    stops: 0,
    aircraft: 'Airbus A320',
    class: 'Economy',
    price: {
      currency: '£',
      amount: '189'
    },
    baggage: {
      cabin: '8kg',
      checked: '23kg'
    },
    amenities: ['WiFi', 'In-flight Entertainment', 'Meal Service']
  };

  const handleLocationBook = () => {
    console.log('Booking location:', sampleLocation.name);
  };

  const handleLocationViewDetails = () => {
    console.log('Viewing details for:', sampleLocation.name);
  };

  const handleFlightBook = () => {
    console.log('Booking flight:', sampleFlight.id);
  };

  const handleFlightSelect = () => {
    console.log('Selecting flight:', sampleFlight.id);
  };

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-4xl mx-auto space-y-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-800 mb-2">
            Travel Cards Demo
          </h1>
          <p className="text-gray-600">
            Interactive location and flight cards for chat responses
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Location Card Demo */}
          <div className="space-y-4">
            <h2 className="text-xl font-semibold text-gray-800">
              Location Card
            </h2>
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <LocationCard
                location={sampleLocation}
                onBookClick={handleLocationBook}
                onViewDetailsClick={handleLocationViewDetails}
              />
            </div>
          </div>

          {/* Flight Card Demo */}
          <div className="space-y-4">
            <h2 className="text-xl font-semibold text-gray-800">
              Flight Card
            </h2>
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <FlightCard
                flight={sampleFlight}
                onBookClick={handleFlightBook}
                onSelectClick={handleFlightSelect}
              />
            </div>
          </div>
        </div>

        {/* Usage Instructions */}
        <div className="bg-white p-6 rounded-lg shadow-sm">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">
            Usage Instructions
          </h3>
          <div className="space-y-3 text-gray-600">
            <p>
              <strong>Location Cards:</strong> Click on the card header to expand/collapse 
              and view detailed information including highlights, activities, and pricing.
            </p>
            <p>
              <strong>Flight Cards:</strong> Click on the card header to expand/collapse 
              and view detailed flight information including baggage allowance and amenities.
            </p>
            <p>
              <strong>Integration:</strong> These cards are designed to be embedded within 
              chat messages by adding a `cards` array to the message object with the 
              appropriate data structure.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CardsDemo;
